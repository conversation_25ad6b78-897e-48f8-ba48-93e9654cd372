body {
    background: #111;
    color: #FFD600;
    font-family: 'Segoe UI', Arial, sans-serif;
    margin: 0;
    padding: 0;
}

main {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-height: 60vh;
}

.video-section {
    background: #181818;
    border-radius: 20px;
    box-shadow: 0 2px 16px #FFD60022;
    padding: 1.5rem;
    margin-bottom: 2rem;
    max-width: 500px;
    width: 100%;
    text-align: center;
}
.video-section h2 {
    color: #FFD600;
}
.video-section video {
    width: 100%;
    border-radius: 16px;
    margin-top: 1rem;
}

.specs-table {
    width: 100%;
    max-width: 500px;
    margin: 0 auto 2rem auto;
    background: #222;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 2px 16px #FFD60022;
}
.specs-table table {
    width: 100%;
    border-collapse: collapse;
}
.specs-table th, .specs-table td {
    padding: 0.8rem 1rem;
    text-align: left;
}
.specs-table th {
    background: #FFD600;
    color: #111;
}
.specs-table tr:nth-child(even) {
    background: #181818;
}
.specs-table tr:nth-child(odd) {
    background: #222;
}

.faq-section {
    max-width: 500px;
    margin: 0 auto 2rem auto;
    background: #222;
    border-radius: 16px;
    box-shadow: 0 2px 16px #FFD60022;
    padding: 1.5rem;
}
.faq-section h2 {
    color: #FFD600;
    margin-top: 0;
}
.faq-item {
    margin-bottom: 1rem;
}
.faq-question {
    background: #FFD600;
    color: #111;
    padding: 0.7rem 1rem;
    border-radius: 10px;
    cursor: pointer;
    font-weight: bold;
    transition: background 0.2s;
}
.faq-question:hover {
    background: #ffe066;
}
.faq-answer {
    display: none;
    background: #181818;
    color: #FFD600;
    padding: 0.7rem 1rem;
    border-radius: 10px;
    margin-top: 0.3rem;
}
.faq-item.active .faq-answer {
    display: block;
}

.social-icons {
    text-align: center;
    margin-bottom: 1.5rem;
}
.social-icons a {
    display: inline-block;
    margin: 0 0.5rem;
    color: #FFD600;
    font-size: 2rem;
    transition: color 0.2s;
}
.social-icons a:hover {
    color: #fff;
}

@media (max-width: 600px) {
    .promo, .video-section, .specs-table, .faq-section {
        max-width: 99vw;
        width: 99vw;
        padding: 0.7rem;
        border-radius: 10px;
        box-sizing: border-box;
    }
    .iphone-img {
        width: 90vw;
        max-width: 180px;
        min-width: 100px;
    }
    .specs-table th, .specs-table td {
        padding: 0.5rem 0.3rem;
        font-size: 0.95rem;
    }
    .faq-question, .faq-answer {
        font-size: 1rem;
        padding: 0.5rem 0.7rem;
    }
    .buy-btn {
        width: 90vw;
        max-width: 250px;
        font-size: 1rem;
        padding: 0.7rem 0;
    }
    header {
        padding: 1.2rem 0.2rem 0.7rem 0.2rem;
        border-bottom-left-radius: 18px;
        border-bottom-right-radius: 18px;
    }
    footer {
        font-size: 0.85rem;
        padding: 0.7rem;
    }
}
}

header {
    background: #FFD600;
    color: #111;
    text-align: center;
    padding: 2rem 1rem 1rem 1rem;
    border-bottom-left-radius: 40px;
    border-bottom-right-radius: 40px;
}

header h1 {
    margin: 0;
    font-size: 2.5rem;
    letter-spacing: 2px;
}

header p {
    margin: 0.5rem 0 0 0;
    font-size: 1.2rem;
}

main {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60vh;
}

.promo {
    background: #222;
    border-radius: 24px;
    box-shadow: 0 4px 24px #000a;
    padding: 2rem;
    max-width: 400px;
    text-align: center;
}

.iphone-img {
    width: 180px;
    margin-bottom: 1.5rem;
    border-radius: 20px;
    box-shadow: 0 2px 16px #FFD60055;
}

.promo h2 {
    color: #FFD600;
    margin-top: 0;
}

.promo ul {
    list-style: none;
    padding: 0;
    margin: 1rem 0;
}

.promo ul li {
    background: #FFD600;
    color: #111;
    margin: 0.5rem 0;
    padding: 0.5rem 1rem;
    border-radius: 12px;
    font-weight: bold;
}

.buy-btn {
    display: inline-block;
    background: #FFD600;
    color: #111;
    padding: 0.8rem 2rem;
    border-radius: 24px;
    text-decoration: none;
    font-weight: bold;
    font-size: 1.1rem;
    margin-top: 1rem;
    transition: background 0.2s, color 0.2s;
}

.buy-btn:hover {
    background: #111;
    color: #FFD600;
    border: 2px solid #FFD600;
}

footer {
    text-align: center;
    padding: 1rem;
    background: #111;
    color: #FFD600;
    font-size: 0.95rem;
    border-top: 1px solid #FFD60033;
}
